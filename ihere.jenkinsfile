
import java.text.SimpleDateFormat

pipeline {
    agent any
    tools { nodejs "n14" }
    
    environment {
        // Git 配置 - 外部git仓库
        GIT_REPOSITORY = 'http://*************:19524/consumer-web-frontend/here-admin-front-web.git'
        GIT_CREDENTIALS_ID = 'cicd'
        // 项目配置
        PROJECT_DIR = 'here-admin-front-web'
        BUILD_OUTPUT_DIR = 'dist'
        PACKAGE_NAME = 'frontend-dist.tar.gz'
       
        // 服务器配置 - 支持多服务器部署
        SERVER_IPS = '*************:18586,***************:18586,************:18586'
        SERVER_USER = 'DeployUser'
        SSH_KEY = 'JENKINS_SSH'
        
        // 部署路径配置
        DEPLOY_BASE_PATH = '/home/<USER>/openresty/nginx'
        DEPLOY_DIR_NAME = 'html'
        
        // OSS 配置
        OSS_BUCKET_NAME = 'seeworld-internal'
        OSS_INTER_ENDPOINT = 'oss-cn-beijing-internal.aliyuncs.com'
        OSS_OUTER_ENDPOINT = 'oss-cn-beijing.aliyuncs.com'
        OSS_FOLDER = 'devops/frontend'
    }
    
    parameters {
        string(name: 'branch', defaultValue: 'main', description: 'Git分支名称')
        choice(name: 'isInstall', choices: ['no', 'yes'], description: '是否重新安装依赖')
    }

    stages {
        stage('Checkout') {
            steps {
                withCredentials([usernamePassword(credentialsId: "${GIT_CREDENTIALS_ID}", usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                    checkout([$class: 'GitSCM', 
                        branches: [[name: "*/${params.branch}"]], 
                        extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: "${PROJECT_DIR}"]], 
                        userRemoteConfigs: [[
                            credentialsId: "${GIT_CREDENTIALS_ID}", 
                            url: "${GIT_REPOSITORY}"
                        ]]
                    ])
                }
            }
        }

        stage('Build') {
            steps {
                script {
                    def now = new Date()
                    def sdf = new SimpleDateFormat("yyyyMMddHHmm")
                    sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"))
                    def formattedDate = sdf.format(now)
                    env.CURRENT_DATETIME = formattedDate
                    echo "构建时间戳: ${env.CURRENT_DATETIME}"
                }
                
                sh '''
                    echo "开始构建前端项目..."
                    pwd
                    node -v
                    npm -v
                    
                    cd ${PROJECT_DIR}
                    
                    if [ "$isInstall" = "yes" ]; then
                        echo "清理依赖并重新安装..."
                        rm -rf node_modules package-lock.json
                        npm install
                    else
                        echo "跳过依赖安装"
                    fi
                    
                    echo "执行构建..."
                    npm run build
                    
                    echo "构建完成"
                '''
            }
        }

        stage('Package Build Artifacts') {
            steps {
                script {
                    def packageName = env.PACKAGE_NAME.replaceAll(/\.tar\.gz$/, '')
                    def ossPackageName = "${packageName}-${env.CURRENT_DATETIME}.tar.gz"
                    env.OSS_PACKAGE_NAME = ossPackageName
                    
                    sh '''
                        cd ${PROJECT_DIR}
                        
                        echo "打包构建产物..."
                        rm -rf *.tar.gz
                        tar -zcvf ${OSS_PACKAGE_NAME} -C ${BUILD_OUTPUT_DIR} .
                        
                        echo "打包完成: ${OSS_PACKAGE_NAME}"
                        ls -la ${OSS_PACKAGE_NAME}
                    '''
                }
            }
        }

        stage('Upload to OSS') {
            steps {
                withCredentials([
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_ID', variable: 'ALI_KEY_ID'),
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_SECRET', variable: 'ALI_KEY_SECRET')
                ]) {
                    script {
                        def ossPackageName = env.OSS_PACKAGE_NAME

                        sh """
                            echo "上传到 OSS..."
                            
                            if [ ! -f ossutil64 ]; then
                                echo "下载 ossutil64 工具..."
                                curl -O https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil64
                                chmod +x ossutil64
                            fi

                            ./ossutil64 config -e ${OSS_INTER_ENDPOINT} -i ${ALI_KEY_ID} -k ${ALI_KEY_SECRET} --output-dir=/tmp
                            ./ossutil64 cp ${PROJECT_DIR}/${ossPackageName} oss://${OSS_BUCKET_NAME}/${OSS_FOLDER}/${ossPackageName} -f
                            
                            echo "前端包已上传到 OSS: ${ossPackageName}"
                        """
                    }
                }
            }
        }

        stage('Deploy to Servers') {
            steps {
                withCredentials([
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_ID', variable: 'ALI_KEY_ID'),
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_SECRET', variable: 'ALI_KEY_SECRET')
                ]) {
                    script {
                        def serverDetails = SERVER_IPS.split(',')
                        def deploySuccess = true
                        def ossPackageName = env.OSS_PACKAGE_NAME
    
                        serverDetails.each { serverDetail ->
                            def (serverIp, serverPort) = serverDetail.split(':')
                            echo "开始测试部署到服务器: ${serverIp}:${serverPort}"
    
                            sshagent([SSH_KEY]) {
                                def deployResult = sh(returnStatus: true, script: """
                                    ssh -p ${serverPort} -o StrictHostKeyChecking=no ${SERVER_USER}@${serverIp} '
                                    echo "[测试模式 - 仅下载OSS包并添加日志]"
                                    
                                    # 切换到有权限的html目录
                                    cd ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}

                                    echo "[从 OSS 下载前端包: ${ossPackageName}]"
                                    if [ ! -f ossutil64 ]; then
                                        echo "下载 ossutil64 工具..."
                                        curl -O https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil64
                                        chmod +x ossutil64
                                    fi

                                    ./ossutil64 config -e ${OSS_OUTER_ENDPOINT} -i ${ALI_KEY_ID} -k ${ALI_KEY_SECRET} --output-dir=/tmp
                                    ./ossutil64 cp oss://${OSS_BUCKET_NAME}/${OSS_FOLDER}/${ossPackageName} ${ossPackageName} -f

                                    echo "[添加成功日志到当前目录]"
                                    # 创建success.log文件
                                    echo "部署成功 - 时间: \$(date)" > success.log
                                    echo "包名: ${ossPackageName}" >> success.log
                                    echo "服务器: ${serverIp}:${serverPort}" >> success.log
                                    echo "下载路径: ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}" >> success.log
                                    
                                    echo "success.log已创建在当前目录"

                                    # 清理下载的包（保留success.log）
                                    rm -f ${ossPackageName}

                                    echo "测试完成 - OSS包已下载并清理，success.log已添加"
                                    echo "当前目录文件数量: \$(find . -type f | wc -l)"
                                    ls -la success.log
                                    '
                                """)
    
                                if (deployResult != 0) {
                                    echo "测试部署到 ${serverIp}:${serverPort} 失败，退出码: ${deployResult}"
                                    deploySuccess = false
                                } else {
                                    echo "服务器 ${serverIp}:${serverPort} 测试完成"
                                }
                            }
                        }
    
                        if (!deploySuccess) {
                            currentBuild.result = 'FAILURE'
                            error('一个或多个服务器测试失败')
                        }
                    }
                }
            }
        }

        stage('Clean Up') {
            steps {
                script {
                    def serverDetails = SERVER_IPS.split(',')
                    serverDetails.each { serverDetail ->
                        def (serverIp, serverPort) = serverDetail.split(':')
                        echo "清理服务器 ${serverIp}:${serverPort} 上的旧备份..."
                        sshagent([SSH_KEY]) {
                            sh """
                                ssh -p ${serverPort} -o StrictHostKeyChecking=no ${SERVER_USER}@${serverIp} '
                                cd ${DEPLOY_BASE_PATH}
                                # 只保留最近5个备份，删除更早的
                                ls -t ${DEPLOY_DIR_NAME}-backup-*.tar.gz 2>/dev/null | tail -n +6 | xargs rm -f || true
                                echo "旧备份清理完成"
                                '
                            """
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '前端项目构建和部署成功！'
            echo "部署包: ${env.OSS_PACKAGE_NAME}"
            echo "部署时间: ${env.CURRENT_DATETIME}"
        }
        failure {
            echo '前端项目构建或部署失败，请检查日志！'
        }
        always {
            // 清理工作空间中的临时文件
            sh '''
                rm -f ossutil64
                echo "Jenkins 工作空间清理完成"
            '''
        }
    }
}
