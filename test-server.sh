#!/bin/bash

# 服务器连接测试脚本
# 用于测试 ***************:18586 服务器连接并写入 success.log

set -e  # 遇到错误立即退出

# 配置变量
TARGET_SERVER_IP="***************"
TARGET_SERVER_PORT="18586"
SERVER_USER="DeployUser"
DEPLOY_BASE_PATH="/home/<USER>/openresty/nginx"
DEPLOY_DIR_NAME="html"
CURRENT_DATETIME=$(date '+%Y-%m-%d %H:%M:%S')

echo "=========================================="
echo "服务器连接测试脚本"
echo "=========================================="
echo "目标服务器: ${TARGET_SERVER_IP}:${TARGET_SERVER_PORT}"
echo "测试时间: ${CURRENT_DATETIME}"
echo "目标目录: ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}"
echo "=========================================="

# 检查 SSH 连接
echo "🔍 检查服务器连接..."
if ! ssh -p ${TARGET_SERVER_PORT} -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${SERVER_USER}@${TARGET_SERVER_IP} "echo '连接成功'" > /dev/null 2>&1; then
    echo "❌ 无法连接到服务器 ${TARGET_SERVER_IP}:${TARGET_SERVER_PORT}"
    echo "请检查："
    echo "1. 服务器是否在线"
    echo "2. SSH 端口是否正确"
    echo "3. SSH 密钥是否配置正确"
    echo "4. 网络连接是否正常"
    exit 1
fi

echo "✅ 服务器连接正常"

# 执行测试
echo "🚀 开始执行服务器测试..."

ssh -p ${TARGET_SERVER_PORT} -o StrictHostKeyChecking=no ${SERVER_USER}@${TARGET_SERVER_IP} << 'EOF'
set -e

echo "=== 服务器信息 ==="
echo "主机名: $(hostname)"
echo "当前用户: $(whoami)"
echo "当前时间: $(date)"
echo "系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || uname -s)"
echo "系统架构: $(uname -m)"
echo ""

echo "=== 检查目标目录 ==="
TARGET_DIR="/home/<USER>/openresty/nginx/html"
echo "目标路径: $TARGET_DIR"

if [ -d "$TARGET_DIR" ]; then
    echo "✅ 目标目录存在"
    echo "目录权限: $(ls -ld $TARGET_DIR)"
    echo "目录大小: $(du -sh $TARGET_DIR 2>/dev/null || echo '无法获取')"
    echo "文件数量: $(find $TARGET_DIR -type f 2>/dev/null | wc -l) 个文件"
else
    echo "⚠️  目标目录不存在，尝试创建..."
    if mkdir -p "$TARGET_DIR"; then
        echo "✅ 目录创建成功"
    else
        echo "❌ 目录创建失败"
        exit 1
    fi
fi

echo ""
echo "=== 系统资源检查 ==="
echo "磁盘空间:"
df -h $TARGET_DIR | head -2
echo ""
echo "内存使用:"
free -h | head -2
echo ""
echo "负载情况:"
uptime

echo ""
echo "=== 网络连接测试 ==="
echo "本机IP: $(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo '无法获取')"
echo "DNS测试: $(nslookup baidu.com 2>/dev/null | grep -A1 'Non-authoritative answer' | tail -1 | awk '{print $2}' || echo 'DNS测试失败')"

echo ""
echo "=== 写入测试文件 ==="
cd "$TARGET_DIR"

# 创建详细的 success.log 文件
cat > success.log << LOGEOF
=== 服务器连接测试成功报告 ===
测试时间: $(date '+%Y-%m-%d %H:%M:%S %Z')
服务器地址: ***************:18586
用户账户: $(whoami)
目标目录: $TARGET_DIR
工作目录: $(pwd)

=== 服务器基本信息 ===
主机名: $(hostname)
系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || uname -s)
内核版本: $(uname -r)
系统架构: $(uname -m)
运行时间: $(uptime | awk -F'up ' '{print $2}' | awk -F',' '{print $1}')

=== 硬件资源状态 ===
CPU信息: $(grep 'model name' /proc/cpuinfo | head -1 | cut -d':' -f2 | sed 's/^ *//' || echo '无法获取')
CPU核心数: $(nproc)
内存总量: $(free -h | awk 'NR==2{print $2}')
内存使用: $(free -h | awk 'NR==2{print $3}')
磁盘空间: $(df -h $TARGET_DIR | tail -1 | awk '{print "总计:" $2 " 已用:" $3 " 可用:" $4 " 使用率:" $5}')

=== 网络连接状态 ===
本机IP: $(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo '无法获取')
网关: $(ip route | grep default | awk '{print $3}' | head -1 || echo '无法获取')
DNS服务器: $(cat /etc/resolv.conf | grep nameserver | head -1 | awk '{print $2}' || echo '无法获取')

=== 目录权限测试 ===
当前目录: $(pwd)
目录所有者: $(ls -ld . | awk '{print $3":"$4}')
目录权限: $(ls -ld . | awk '{print $1}')
写权限测试: $(touch .test_write_$(date +%s) 2>/dev/null && rm -f .test_write_* && echo "✅ 可写" || echo "❌ 不可写")

=== 进程和服务状态 ===
当前进程数: $(ps aux | wc -l)
nginx状态: $(ps aux | grep nginx | grep -v grep | wc -l) 个进程
系统负载: $(uptime | awk -F'load average:' '{print $2}')

=== 测试结果 ===
连接状态: ✅ 成功
目录访问: ✅ 正常
文件写入: ✅ 成功
权限检查: ✅ 通过
网络连接: ✅ 正常

测试完成时间: $(date '+%Y-%m-%d %H:%M:%S %Z')
测试状态: 全部通过 ✅

=== 备注 ===
此文件由服务器连接测试脚本自动生成
如需重新测试，请删除此文件后重新运行测试脚本
LOGEOF

if [ -f success.log ]; then
    echo "✅ success.log 文件创建成功"
    echo "文件路径: $(pwd)/success.log"
    echo "文件大小: $(ls -lh success.log | awk '{print $5}')"
    echo "文件权限: $(ls -l success.log | awk '{print $1}')"
    echo ""
    echo "=== success.log 内容预览 (前10行) ==="
    head -10 success.log
    echo "..."
    echo "=== success.log 内容预览 (后5行) ==="
    tail -5 success.log
else
    echo "❌ success.log 文件创建失败"
    exit 1
fi

echo ""
echo "=== 最终验证 ==="
echo "文件确认: $([ -f success.log ] && echo '✅ 存在' || echo '❌ 不存在')"
echo "文件行数: $(wc -l < success.log)"
echo "最后修改: $(stat -c %y success.log 2>/dev/null || ls -l success.log | awk '{print $6,$7,$8}')"

EOF

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "🎉 服务器测试完成！"
    echo "=========================================="
    echo "✅ 连接测试: 成功"
    echo "✅ 目录访问: 正常"  
    echo "✅ 文件写入: 成功"
    echo "✅ success.log: 已创建"
    echo ""
    echo "success.log 文件位置:"
    echo "${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}/success.log"
    echo ""
    echo "如需查看完整日志，请登录服务器执行:"
    echo "ssh -p ${TARGET_SERVER_PORT} ${SERVER_USER}@${TARGET_SERVER_IP}"
    echo "cat ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}/success.log"
    echo "=========================================="
else
    echo ""
    echo "=========================================="
    echo "❌ 服务器测试失败！"
    echo "=========================================="
    echo "请检查以下项目："
    echo "1. 服务器网络连接"
    echo "2. SSH 密钥配置"
    echo "3. 用户权限设置"
    echo "4. 目录访问权限"
    echo "=========================================="
    exit 1
fi
